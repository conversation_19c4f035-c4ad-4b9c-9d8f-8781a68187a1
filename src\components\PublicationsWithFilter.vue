<script setup lang="ts">
import { ref, computed } from "vue";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import type { Publication } from "@/data/portfolio";
import { sectionTitles } from "@/data/portfolio";

interface Props {
    publications: Publication[];
}

const props = defineProps<Props>();

const activeTab = ref("all");

const filteredPublications = computed(() => {
    switch (activeTab.value) {
        case "published":
            return props.publications.filter((pub) => pub.type === "published");
        case "unpublished":
            return props.publications.filter((pub) => pub.type === "unpublished");
        default:
            return props.publications;
    }
});

const publishedPapers = computed(() => props.publications.filter((pub) => pub.type === "published"));

const unpublishedPapers = computed(() => props.publications.filter((pub) => pub.type === "unpublished"));

const getStatusText = computed(() => {
    switch (activeTab.value) {
        case "published":
            return `Showing ${publishedPapers.value.length} published paper${
                publishedPapers.value.length !== 1 ? "s" : ""
            }.`;
        case "unpublished":
            return `Showing ${unpublishedPapers.value.length} unpublished paper${
                unpublishedPapers.value.length !== 1 ? "s" : ""
            }.`;
        default:
            return `Showing all ${props.publications.length} paper${props.publications.length !== 1 ? "s" : ""}.`;
    }
});
</script>

<template>
    <section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
        <!-- Section Header -->
        <div class="border-l-4 border-sky-600 pl-4 mb-8">
            <h2 id="pub-title" class="text-2xl text-slate-700 font-normal">{{ sectionTitles.publications }}</h2>
        </div>

        <!-- Filter Tabs -->
        <div class="mb-6">
            <p class="text-sm text-slate-500 mb-3">Filter:</p>

            <Tabs v-model="activeTab" class="w-full">
                <TabsList class="grid w-fit grid-cols-3 bg-slate-100 p-1">
                    <TabsTrigger
                        value="all"
                        class="data-[state=active]:bg-white data-[state=active]:text-slate-900 text-slate-600 hover:text-slate-900 transition-colors"
                    >
                        All ({{ publications.length }})
                    </TabsTrigger>
                    <TabsTrigger
                        value="published"
                        class="data-[state=active]:bg-white data-[state=active]:text-sky-700 text-slate-600 hover:text-slate-900 transition-colors"
                    >
                        Published ({{ publishedPapers.length }})
                    </TabsTrigger>
                    <TabsTrigger
                        value="unpublished"
                        class="data-[state=active]:bg-white data-[state=active]:text-amber-700 text-slate-600 hover:text-slate-900 transition-colors"
                    >
                        Unpublished ({{ unpublishedPapers.length }})
                    </TabsTrigger>
                </TabsList>

                <p class="text-sm text-slate-400 mt-3">{{ getStatusText }}</p>

                <!-- All Publications -->
                <TabsContent value="all" class="space-y-10 mt-6">
                    <!-- Published Papers -->
                    <div v-if="publishedPapers.length > 0">
                        <div class="flex items-center gap-2 mb-6">
                            <div class="w-3 h-3 bg-sky-600 rounded-full"></div>
                            <h3 class="text-lg text-slate-700 font-medium">Published</h3>
                        </div>

                        <div class="space-y-6">
                            <article
                                v-for="(publication, index) in publishedPapers"
                                :key="publication.id"
                                class="flex flex-col sm:flex-row gap-4 items-start"
                            >
                                <!-- Left Side: Index Number and Thumbnail (outside card) -->
                                <div
                                    class="flex-shrink-0 flex sm:flex-col flex-row items-center gap-3 w-full sm:w-auto justify-center sm:justify-start"
                                >
                                    <!-- Index Number (hidden on mobile, shown on larger screens) -->
                                    <div
                                        class="hidden sm:flex w-12 h-12 bg-sky-50 border border-sky-200 rounded-full items-center justify-center"
                                    >
                                        <span class="text-sky-700 font-bold text-sm">{{
                                            String(index + 1).padStart(2, "0")
                                        }}</span>
                                    </div>

                                    <!-- Thumbnail -->
                                    <div
                                        class="relative w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center flex-shrink-0"
                                    >
                                        <img
                                            :src="publication.thumbnail"
                                            width="64"
                                            height="64"
                                            alt="Publication thumbnail"
                                            class="w-full h-full object-cover"
                                        />
                                        <!-- Mobile: Index number overlay on thumbnail -->
                                        <div
                                            class="absolute top-1 left-1 w-6 h-6 bg-sky-600 text-white rounded-full flex items-center justify-center text-xs font-bold sm:hidden shadow-sm"
                                        >
                                            {{ String(index + 1).padStart(2, "0") }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Side: Content Card -->
                                <div
                                    class="flex-1 min-w-0 bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full sm:w-auto"
                                >
                                    <p
                                        class="text-sm text-slate-500 mb-1 break-words"
                                        v-html="publication.authorsHtml || publication.authors"
                                    ></p>
                                    <h4
                                        class="text-slate-800 font-semibold mb-1 leading-tight break-words"
                                        v-html="publication.titleHtml || publication.title"
                                    ></h4>
                                    <p
                                        class="text-sm text-slate-600 mb-2 break-words"
                                        v-html="publication.journalHtml || publication.journal"
                                    ></p>

                                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs">
                                        <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded break-words">
                                            <span v-if="publication.notesHtml" v-html="publication.notesHtml"></span>
                                            <span v-else>{{ publication.notes }}</span>
                                        </span>
                                        <a
                                            v-if="publication.furtherInfoLink"
                                            :href="publication.furtherInfoLink"
                                            class="text-sky-600 hover:text-sky-800 underline break-words"
                                        >
                                            {{ publication.furtherInfoText || "further information" }}
                                        </a>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </div>

                    <!-- Unpublished Papers -->
                    <div v-if="unpublishedPapers.length > 0">
                        <div class="flex items-center gap-2 mb-6">
                            <div class="w-3 h-3 bg-amber-500 rounded-full"></div>
                            <h3 class="text-lg text-slate-700 font-medium">Unpublished / Working Papers</h3>
                        </div>

                        <div class="space-y-6">
                            <article
                                v-for="(publication, index) in unpublishedPapers"
                                :key="publication.id"
                                class="flex flex-col sm:flex-row gap-4 items-start"
                            >
                                <!-- Left Side: Index Number and Thumbnail (outside card) -->
                                <div
                                    class="flex-shrink-0 flex sm:flex-col flex-row items-center gap-3 w-full sm:w-auto justify-center sm:justify-start"
                                >
                                    <!-- Index Number (hidden on mobile, shown on larger screens) -->
                                    <div
                                        class="hidden sm:flex w-12 h-12 bg-amber-50 border border-amber-200 rounded-full items-center justify-center"
                                    >
                                        <span class="text-amber-700 font-bold text-sm">{{
                                            String(index + 1).padStart(2, "0")
                                        }}</span>
                                    </div>

                                    <!-- Thumbnail -->
                                    <div
                                        class="relative w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center flex-shrink-0"
                                    >
                                        <img
                                            :src="publication.thumbnail"
                                            width="64"
                                            height="64"
                                            alt="Working paper thumbnail"
                                            class="w-full h-full object-cover"
                                        />
                                        <!-- Mobile: Index number overlay on thumbnail -->
                                        <div
                                            class="absolute top-1 left-1 w-6 h-6 bg-amber-600 text-white rounded-full flex items-center justify-center text-xs font-bold sm:hidden shadow-sm"
                                        >
                                            {{ String(index + 1).padStart(2, "0") }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Side: Content Card -->
                                <div
                                    class="flex-1 min-w-0 bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full sm:w-auto"
                                >
                                    <p
                                        class="text-sm text-slate-500 mb-1 break-words"
                                        v-html="publication.authorsHtml || publication.authors"
                                    ></p>
                                    <h4
                                        class="text-slate-800 font-semibold mb-1 leading-tight break-words"
                                        v-html="publication.titleHtml || publication.title"
                                    ></h4>
                                    <p
                                        class="text-sm text-slate-600 mb-2 break-words"
                                        v-html="publication.journalHtml || publication.journal"
                                    ></p>

                                    <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs">
                                        <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded break-words">
                                            <span v-if="publication.notesHtml" v-html="publication.notesHtml"></span>
                                            <span v-else>{{ publication.notes }}</span>
                                        </span>
                                        <a
                                            v-if="publication.furtherInfoLink"
                                            :href="publication.furtherInfoLink"
                                            class="text-sky-600 hover:text-sky-800 underline break-words"
                                        >
                                            {{ publication.furtherInfoText || "further information" }}
                                        </a>
                                    </div>
                                </div>
                            </article>
                        </div>
                    </div>
                </TabsContent>

                <!-- Published Only -->
                <TabsContent value="published" class="space-y-6 mt-6">
                    <div class="flex items-center gap-2 mb-6">
                        <div class="w-3 h-3 bg-sky-600 rounded-full"></div>
                        <h3 class="text-lg text-slate-700 font-medium">{{ sectionTitles.published }}</h3>
                    </div>

                    <article
                        v-for="(publication, index) in publishedPapers"
                        :key="publication.id"
                        class="flex flex-col sm:flex-row gap-4 items-start"
                    >
                        <!-- Left Side: Index Number and Thumbnail (outside card) -->
                        <div
                            class="flex-shrink-0 flex sm:flex-col flex-row items-center gap-3 w-full sm:w-auto justify-center sm:justify-start"
                        >
                            <!-- Index Number (hidden on mobile, shown on larger screens) -->
                            <div
                                class="hidden sm:flex w-12 h-12 bg-sky-50 border border-sky-200 rounded-full items-center justify-center"
                            >
                                <span class="text-sky-700 font-bold text-sm">{{
                                    String(index + 1).padStart(2, "0")
                                }}</span>
                            </div>

                            <!-- Thumbnail -->
                            <div
                                class="relative w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center flex-shrink-0"
                            >
                                <img
                                    :src="publication.thumbnail"
                                    width="64"
                                    height="64"
                                    alt="Publication thumbnail"
                                    class="w-full h-full object-cover"
                                />
                                <!-- Mobile: Index number overlay on thumbnail -->
                                <div
                                    class="absolute top-1 left-1 w-6 h-6 bg-sky-600 text-white rounded-full flex items-center justify-center text-xs font-bold sm:hidden shadow-sm"
                                >
                                    {{ String(index + 1).padStart(2, "0") }}
                                </div>
                            </div>
                        </div>

                        <!-- Right Side: Content Card -->
                        <div
                            class="flex-1 min-w-0 bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full sm:w-auto"
                        >
                            <p
                                class="text-sm text-slate-500 mb-1 break-words"
                                v-html="publication.authorsHtml || publication.authors"
                            ></p>
                            <h4
                                class="text-slate-800 font-semibold mb-1 leading-tight break-words"
                                v-html="publication.titleHtml || publication.title"
                            ></h4>
                            <p
                                class="text-sm text-slate-600 mb-2 break-words"
                                v-html="publication.journalHtml || publication.journal"
                            ></p>

                            <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs">
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded break-words">
                                    <span v-if="publication.notesHtml" v-html="publication.notesHtml"></span>
                                    <span v-else>{{ publication.notes }}</span>
                                </span>
                                <a
                                    v-if="publication.furtherInfoLink"
                                    :href="publication.furtherInfoLink"
                                    class="text-sky-600 hover:text-sky-800 underline break-words"
                                >
                                    {{ publication.furtherInfoText || "further information" }}
                                </a>
                            </div>
                        </div>
                    </article>
                </TabsContent>

                <!-- Unpublished Only -->
                <TabsContent value="unpublished" class="space-y-6 mt-6">
                    <div class="flex items-center gap-2 mb-6">
                        <div class="w-3 h-3 bg-amber-500 rounded-full"></div>
                        <h3 class="text-lg text-slate-700 font-medium">{{ sectionTitles.unpublished }}</h3>
                    </div>

                    <article
                        v-for="(publication, index) in unpublishedPapers"
                        :key="publication.id"
                        class="flex flex-col sm:flex-row gap-4 items-start"
                    >
                        <!-- Left Side: Index Number and Thumbnail (outside card) -->
                        <div
                            class="flex-shrink-0 flex sm:flex-col flex-row items-center gap-3 w-full sm:w-auto justify-center sm:justify-start"
                        >
                            <!-- Index Number (hidden on mobile, shown on larger screens) -->
                            <div
                                class="hidden sm:flex w-12 h-12 bg-amber-50 border border-amber-200 rounded-full items-center justify-center"
                            >
                                <span class="text-amber-700 font-bold text-sm">{{
                                    String(index + 1).padStart(2, "0")
                                }}</span>
                            </div>

                            <!-- Thumbnail -->
                            <div
                                class="relative w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center flex-shrink-0"
                            >
                                <img
                                    :src="publication.thumbnail"
                                    width="64"
                                    height="64"
                                    alt="Working paper thumbnail"
                                    class="w-full h-full object-cover"
                                />
                                <!-- Mobile: Index number overlay on thumbnail -->
                                <div
                                    class="absolute top-1 left-1 w-6 h-6 bg-amber-600 text-white rounded-full flex items-center justify-center text-xs font-bold sm:hidden shadow-sm"
                                >
                                    {{ String(index + 1).padStart(2, "0") }}
                                </div>
                            </div>
                        </div>

                        <!-- Right Side: Content Card -->
                        <div
                            class="flex-1 min-w-0 bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full sm:w-auto"
                        >
                            <p
                                class="text-sm text-slate-500 mb-1 break-words"
                                v-html="publication.authorsHtml || publication.authors"
                            ></p>
                            <h4
                                class="text-slate-800 font-semibold mb-1 leading-tight break-words"
                                v-html="publication.titleHtml || publication.title"
                            ></h4>
                            <p
                                class="text-sm text-slate-600 mb-2 break-words"
                                v-html="publication.journalHtml || publication.journal"
                            ></p>

                            <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs">
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded break-words">
                                    <span v-if="publication.notesHtml" v-html="publication.notesHtml"></span>
                                    <span v-else>{{ publication.notes }}</span>
                                </span>
                                <a
                                    v-if="publication.furtherInfoLink"
                                    :href="publication.furtherInfoLink"
                                    class="text-sky-600 hover:text-sky-800 underline break-words"
                                >
                                    {{ publication.furtherInfoText || "further information" }}
                                </a>
                            </div>
                        </div>
                    </article>
                </TabsContent>
            </Tabs>
        </div>
    </section>
</template>
